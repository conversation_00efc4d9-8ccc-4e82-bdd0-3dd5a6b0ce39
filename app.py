from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from models import db, User, Exercise, ExerciseSession, WeeklyProgress, Achievement, UserAchievement, UserProfile
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import os
from werkzeug.utils import secure_filename
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///eddfit.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Inițializare extensii
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Te rugăm să te autentifici pentru a accesa această pagină.'

# Asigură-te că directorul pentru imagini există
exercise_images_dir = os.path.join(app.static_folder, 'images', 'exercises')
if not os.path.exists(exercise_images_dir):
    os.makedirs(exercise_images_dir)

# Configurare pentru încărcarea fișierelor
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Ruta principală - redirect la dashboard sau login
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

# Autentificare
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('Autentificare reușită!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Nume utilizator sau parolă incorectă!', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Te-ai deconectat cu succes!', 'info')
    return redirect(url_for('login'))

# Dashboard principal
@app.route('/dashboard')
@login_required
def dashboard():
    # Obține ziua curentă (1=Luni, 7=Duminică)
    today = datetime.now().weekday() + 1

    # Exercițiile pentru ziua de azi
    today_exercises = Exercise.query.filter_by(day_of_week=today).all()

    # Progresul săptămânii curente
    week_start = datetime.now().date() - timedelta(days=datetime.now().weekday())
    weekly_progress = WeeklyProgress.query.filter_by(
        user_id=current_user.id,
        week_start=week_start
    ).first()

    # Sesiunile de exerciții de azi
    today_sessions = ExerciseSession.query.filter(
        ExerciseSession.user_id == current_user.id,
        ExerciseSession.completed_at >= datetime.now().date()
    ).all()

    # Profilul utilizatorului
    user_profile = UserProfile.query.filter_by(user_id=current_user.id).first()
    if not user_profile:
        user_profile = UserProfile(user_id=current_user.id)
        db.session.add(user_profile)
        db.session.commit()

    # Realizările recente
    recent_achievements = UserAchievement.query.filter_by(user_id=current_user.id)\
                                              .order_by(UserAchievement.earned_at.desc())\
                                              .limit(3).all()

    return render_template('dashboard.html',
                         exercises=today_exercises,
                         weekly_progress=weekly_progress,
                         today_sessions=today_sessions,
                         today=today,
                         user_profile=user_profile,
                         recent_achievements=recent_achievements)

# Exerciții pentru o zi specifică
@app.route('/exercises/<int:day>')
@login_required
def exercises_by_day(day):
    if day < 1 or day > 7:
        flash('Zi invalidă!', 'error')
        return redirect(url_for('dashboard'))

    exercises = Exercise.query.filter_by(day_of_week=day).all()
    days = ['Luni', 'Marți', 'Miercuri', 'Joi', 'Vineri', 'Sâmbătă', 'Duminică']
    day_name = days[day - 1]

    return render_template('exercises.html', exercises=exercises, day=day, day_name=day_name)

# Completare exercițiu (AJAX)
@app.route('/complete_exercise', methods=['POST'])
@login_required
def complete_exercise():
    exercise_id = request.json.get('exercise_id')
    repetitions_completed = request.json.get('repetitions_completed', 0)
    total_duration = request.json.get('total_duration', 0)  # durata totală în secunde

    exercise = Exercise.query.get_or_404(exercise_id)

    # Calculează caloriile arse pe baza repetărilor completate
    completion_percentage = repetitions_completed / exercise.repetitions
    calories = int(exercise.calories_burned * completion_percentage)

    # Salvează sesiunea
    session = ExerciseSession(
        user_id=current_user.id,
        exercise_id=exercise_id,
        repetitions_completed=repetitions_completed,
        total_duration=total_duration,
        calories_burned=calories
    )
    db.session.add(session)

    # Actualizează progresul săptămânal
    week_start = datetime.now().date() - timedelta(days=datetime.now().weekday())
    weekly_progress = WeeklyProgress.query.filter_by(
        user_id=current_user.id,
        week_start=week_start
    ).first()

    if not weekly_progress:
        weekly_progress = WeeklyProgress(
            user_id=current_user.id,
            week_start=week_start,
            total_exercises=1,
            total_calories=calories,
            total_time_minutes=total_duration // 60
        )
        db.session.add(weekly_progress)
    else:
        weekly_progress.total_exercises += 1
        weekly_progress.total_calories += calories
        weekly_progress.total_time_minutes += total_duration // 60

    db.session.commit()

    # Verifică și acordă realizări
    new_achievements = check_and_award_achievements(current_user.id)

    return jsonify({
        'success': True,
        'message': f'Exercițiul completat! {repetitions_completed}/{exercise.repetitions} repetări. Ai ars {calories} calorii!',
        'calories': calories,
        'repetitions': repetitions_completed,
        'total_repetitions': exercise.repetitions,
        'new_achievements': new_achievements
    })

# Admin Panel
@app.route('/admin')
@login_required
def admin():
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    exercises = Exercise.query.all()
    return render_template('admin.html', users=users, exercises=exercises)

# Adăugare utilizator (Admin)
@app.route('/admin/add_user', methods=['POST'])
@login_required
def add_user():
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Nu ai permisiuni!'})

    username = request.form['username']
    email = request.form['email']
    password = request.form['password']
    is_admin = 'is_admin' in request.form

    # Verifică dacă utilizatorul există
    if User.query.filter_by(username=username).first():
        return jsonify({'success': False, 'message': 'Utilizatorul există deja!'})

    user = User(username=username, email=email, is_admin=is_admin)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()

    flash(f'Utilizatorul {username} a fost adăugat cu succes!', 'success')
    return redirect(url_for('admin'))

# Modificare utilizator (Admin)
@app.route('/admin/edit_user/<int:user_id>', methods=['POST'])
@login_required
def edit_user(user_id):
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # Obține datele din formular
    new_username = request.form['username']
    new_email = request.form['email']
    new_password = request.form.get('password')  # Opțional
    is_admin = 'is_admin' in request.form

    # Verifică dacă username-ul nou nu există deja (dacă s-a schimbat)
    if new_username != user.username:
        existing_user = User.query.filter_by(username=new_username).first()
        if existing_user:
            flash('Numele de utilizator există deja!', 'error')
            return redirect(url_for('admin'))

    # Verifică dacă email-ul nou nu există deja (dacă s-a schimbat)
    if new_email != user.email:
        existing_email = User.query.filter_by(email=new_email).first()
        if existing_email:
            flash('Adresa de email există deja!', 'error')
            return redirect(url_for('admin'))

    # Actualizează datele utilizatorului
    user.username = new_username
    user.email = new_email
    user.is_admin = is_admin

    # Actualizează parola doar dacă a fost furnizată
    if new_password and new_password.strip():
        user.set_password(new_password)

    db.session.commit()
    flash(f'Utilizatorul {user.username} a fost actualizat cu succes!', 'success')
    return redirect(url_for('admin'))

# Ștergere utilizator (Admin)
@app.route('/admin/delete_user/<int:user_id>')
@login_required
def delete_user(user_id):
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)
    if user.id == current_user.id:
        flash('Nu te poți șterge pe tine însuți!', 'error')
        return redirect(url_for('admin'))

    username = user.username  # Salvează numele pentru mesaj

    try:
        # Șterge toate datele asociate utilizatorului în ordine
        # 1. Șterge realizările utilizatorului
        UserAchievement.query.filter_by(user_id=user_id).delete()

        # 2. Șterge sesiunile de exerciții
        ExerciseSession.query.filter_by(user_id=user_id).delete()

        # 3. Șterge progresul săptămânal
        WeeklyProgress.query.filter_by(user_id=user_id).delete()

        # 4. Șterge profilul utilizatorului
        UserProfile.query.filter_by(user_id=user_id).delete()

        # 5. În final, șterge utilizatorul
        db.session.delete(user)

        db.session.commit()
        flash(f'Utilizatorul {username} și toate datele asociate au fost șterse cu succes!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Eroare la ștergerea utilizatorului: {str(e)}', 'error')

    return redirect(url_for('admin'))

# Export date utilizator (pentru backup înainte de ștergere)
@app.route('/admin/export_user_data/<int:user_id>')
@login_required
def export_user_data(user_id):
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # Colectează toate datele utilizatorului
    user_data = {
        'user_info': {
            'username': user.username,
            'email': user.email,
            'is_admin': user.is_admin,
            'created_at': user.created_at.isoformat() if user.created_at else None
        },
        'profile': {},
        'exercise_sessions': [],
        'achievements': [],
        'weekly_progress': []
    }

    # Profilul utilizatorului
    profile = UserProfile.query.filter_by(user_id=user_id).first()
    if profile:
        user_data['profile'] = {
            'current_weight': profile.current_weight,
            'target_weight': profile.target_weight,
            'height_cm': profile.height_cm,
            'age': profile.age,
            'gender': profile.gender,
            'activity_level': profile.activity_level,
            'daily_calorie_goal': profile.daily_calorie_goal,
            'streak_days': profile.streak_days,
            'total_points': profile.total_points,
            'last_workout_date': profile.last_workout_date.isoformat() if profile.last_workout_date else None
        }

    # Sesiunile de exerciții
    sessions = ExerciseSession.query.filter_by(user_id=user_id).all()
    for session in sessions:
        user_data['exercise_sessions'].append({
            'exercise_name': session.exercise.name,
            'completed_at': session.completed_at.isoformat(),
            'repetitions_completed': session.repetitions_completed,
            'total_duration': session.total_duration,
            'calories_burned': session.calories_burned
        })

    # Realizările
    achievements = UserAchievement.query.filter_by(user_id=user_id).all()
    for achievement in achievements:
        user_data['achievements'].append({
            'name': achievement.achievement.name,
            'description': achievement.achievement.description,
            'points': achievement.achievement.points,
            'earned_at': achievement.earned_at.isoformat()
        })

    # Progresul săptămânal
    weekly_progress = WeeklyProgress.query.filter_by(user_id=user_id).all()
    for progress in weekly_progress:
        user_data['weekly_progress'].append({
            'week_start': progress.week_start.isoformat(),
            'total_exercises': progress.total_exercises,
            'total_calories': progress.total_calories,
            'total_time_minutes': progress.total_time_minutes,
            'weight_kg': progress.weight_kg
        })

    # Returnează datele ca JSON pentru download
    import json
    from flask import Response

    json_data = json.dumps(user_data, indent=2, ensure_ascii=False)

    response = Response(
        json_data,
        mimetype='application/json',
        headers={
            'Content-Disposition': f'attachment; filename=backup_{user.username}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        }
    )

    flash(f'Backup-ul pentru utilizatorul {user.username} a fost generat!', 'info')
    return response

# Profil utilizator
@app.route('/profile')
@login_required
def profile():
    user_profile = UserProfile.query.filter_by(user_id=current_user.id).first()
    if not user_profile:
        user_profile = UserProfile(user_id=current_user.id)
        db.session.add(user_profile)
        db.session.commit()

    # Realizările utilizatorului
    user_achievements = UserAchievement.query.filter_by(user_id=current_user.id).all()
    total_achievements = Achievement.query.count()

    # Statistici generale
    total_sessions = ExerciseSession.query.filter_by(user_id=current_user.id).count()
    total_calories = db.session.query(db.func.sum(ExerciseSession.calories_burned)).filter_by(user_id=current_user.id).scalar() or 0

    return render_template('profile.html',
                         profile=user_profile,
                         achievements=user_achievements,
                         total_achievements=total_achievements,
                         total_sessions=total_sessions,
                         total_calories=total_calories)

@app.route('/update_profile', methods=['POST'])
@login_required
def update_profile():
    user_profile = UserProfile.query.filter_by(user_id=current_user.id).first()
    if not user_profile:
        user_profile = UserProfile(user_id=current_user.id)
        db.session.add(user_profile)

    # Actualizează datele
    user_profile.current_weight = float(request.form.get('current_weight', 0)) or None
    user_profile.target_weight = float(request.form.get('target_weight', 0)) or None
    user_profile.height_cm = int(request.form.get('height_cm', 0)) or None
    user_profile.age = int(request.form.get('age', 0)) or None
    user_profile.gender = request.form.get('gender')
    user_profile.activity_level = request.form.get('activity_level')
    user_profile.daily_calorie_goal = int(request.form.get('daily_calorie_goal', 200))

    db.session.commit()
    flash('Profilul a fost actualizat cu succes!', 'success')
    return redirect(url_for('profile'))

def check_and_award_achievements(user_id):
    """Verifică și acordă realizări pentru utilizator"""
    new_achievements = []

    # Obține profilul utilizatorului
    user_profile = UserProfile.query.filter_by(user_id=user_id).first()
    if not user_profile:
        user_profile = UserProfile(user_id=user_id)
        db.session.add(user_profile)
        db.session.commit()

    # Calculează statistici
    total_exercises = ExerciseSession.query.filter_by(user_id=user_id).count()
    total_calories = db.session.query(db.func.sum(ExerciseSession.calories_burned)).filter_by(user_id=user_id).scalar() or 0

    # Actualizează streak-ul
    today = datetime.now().date()
    if user_profile.last_workout_date:
        if user_profile.last_workout_date == today - timedelta(days=1):
            user_profile.streak_days += 1
        elif user_profile.last_workout_date != today:
            user_profile.streak_days = 1
    else:
        user_profile.streak_days = 1

    user_profile.last_workout_date = today

    # Verifică realizările
    achievements_to_check = [
        ('first_exercise', 'exercises', 1),
        ('exercise_warrior', 'exercises', 10),
        ('exercise_master', 'exercises', 50),
        ('calorie_burner', 'calories', 100),
        ('calorie_destroyer', 'calories', 500),
        ('streak_starter', 'streak', 3),
        ('streak_master', 'streak', 7),
        ('streak_legend', 'streak', 30),
    ]

    for achievement_name, req_type, req_value in achievements_to_check:
        # Verifică dacă utilizatorul nu are deja această realizare
        existing = UserAchievement.query.join(Achievement).filter(
            UserAchievement.user_id == user_id,
            Achievement.requirement_type == req_type,
            Achievement.requirement_value == req_value
        ).first()

        if not existing:
            current_value = 0
            if req_type == 'exercises':
                current_value = total_exercises
            elif req_type == 'calories':
                current_value = total_calories
            elif req_type == 'streak':
                current_value = user_profile.streak_days

            if current_value >= req_value:
                achievement = Achievement.query.filter_by(
                    requirement_type=req_type,
                    requirement_value=req_value
                ).first()

                if achievement:
                    user_achievement = UserAchievement(
                        user_id=user_id,
                        achievement_id=achievement.id
                    )
                    db.session.add(user_achievement)
                    user_profile.total_points += achievement.points
                    new_achievements.append({
                        'name': achievement.name,
                        'description': achievement.description,
                        'icon': achievement.icon,
                        'points': achievement.points
                    })

    db.session.commit()
    return new_achievements

def init_database():
    """Inițializează baza de date cu exerciții predefinite"""
    db.create_all()

    # Verifică dacă există deja exerciții
    if Exercise.query.first():
        return

    # Exerciții pentru fiecare zi din săptămână (30s x 3 repetări cu 30s pauză)
    exercises_data = [
        # LUNI - Abdomen Superior
        {'name': 'Plank', 'description': 'Poziție de plank pentru întărirea core-ului',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'beginner', 'muscle_group': 'core',
         'calories_burned': 15, 'day_of_week': 1,
         'instructions': '1. Poziție culcat pe burtă\n2. Ridică-te pe coate și vârfuri\n3. Menține corpul drept\n4. Respiră normal\n5. 3 repetări x 30 secunde cu 30s pauză'},

        {'name': 'Crunches', 'description': 'Exercițiu clasic pentru abdomen',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'beginner', 'muscle_group': 'abdomen',
         'calories_burned': 12, 'day_of_week': 1,
         'instructions': '1. Culcat pe spate, genunchii îndoiți\n2. Mâinile la ceafă\n3. Ridică umerii de pe podea\n4. Coboară controlat\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # MARȚI - Cardio + Core
        {'name': 'Mountain Climbers', 'description': 'Exercițiu cardio pentru abdomen',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'cardio',
         'calories_burned': 24, 'day_of_week': 2,
         'instructions': '1. Poziție de plank\n2. Adu genunchiul drept la piept\n3. Schimbă rapid picioarele\n4. Menține ritmul rapid\n5. 3 repetări x 30 secunde cu 30s pauză'},

        {'name': 'Russian Twists', 'description': 'Pentru oblici și abdomen lateral',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'obliques',
         'calories_burned': 18, 'day_of_week': 2,
         'instructions': '1. Șezi cu genunchii îndoiți\n2. Ridică picioarele de pe podea\n3. Rotește trunchiul stânga-dreapta\n4. Menține echilibrul\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # MIERCURI - Abdomen Inferior
        {'name': 'Leg Raises', 'description': 'Pentru abdomenul inferior',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'abdomen',
         'calories_burned': 15, 'day_of_week': 3,
         'instructions': '1. Culcat pe spate, mâinile pe lângă corp\n2. Ridică picioarele drepte\n3. Coboară controlat fără să atingi podeaua\n4. Repetă mișcarea\n5. 3 repetări x 30 secunde cu 30s pauză'},

        {'name': 'Dead Bug', 'description': 'Stabilitate core și coordonare',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'beginner', 'muscle_group': 'core',
         'calories_burned': 12, 'day_of_week': 3,
         'instructions': '1. Culcat pe spate, brațele în sus\n2. Genunchii la 90 grade\n3. Întinde brațul și piciorul opus\n4. Revino la poziția inițială\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # JOI - Cardio Intens
        {'name': 'Burpees', 'description': 'Exercițiu complet pentru arderea caloriilor',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'advanced', 'muscle_group': 'cardio',
         'calories_burned': 36, 'day_of_week': 4,
         'instructions': '1. Poziție în picioare\n2. Coboară în squat, mâinile pe podea\n3. Sari înapoi în plank\n4. Sari înainte și ridică-te\n5. 3 repetări x 30 secunde cu 30s pauză'},

        {'name': 'High Knees', 'description': 'Cardio pentru abdomen și picioare',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'cardio',
         'calories_burned': 21, 'day_of_week': 4,
         'instructions': '1. Aleargă pe loc\n2. Ridică genunchii cât mai sus\n3. Menține ritmul rapid\n4. Balansează brațele\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # VINERI - Combinat
        {'name': 'Bicycle Crunches', 'description': 'Pentru tot abdomenul',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'abdomen',
         'calories_burned': 18, 'day_of_week': 5,
         'instructions': '1. Culcat pe spate, mâinile la ceafă\n2. Adu cotul la genunchiul opus\n3. Alternează părțile\n4. Mișcare de bicicletă\n5. 3 repetări x 30 secunde cu 30s pauză'},

        {'name': 'Plank to Push-up', 'description': 'Combinație plank și flotări',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'advanced', 'muscle_group': 'core',
         'calories_burned': 24, 'day_of_week': 5,
         'instructions': '1. Începe în plank pe coate\n2. Ridică-te pe mâini\n3. Coboară înapoi pe coate\n4. Alternează mișcarea\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # SÂMBĂTĂ - Recuperare Activă
        {'name': 'Side Plank', 'description': 'Pentru oblici și stabilitate',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'intermediate', 'muscle_group': 'obliques',
         'calories_burned': 12, 'day_of_week': 6,
         'instructions': '1. Culcat pe o parte\n2. Ridică-te pe cot\n3. Corpul în linie dreaptă\n4. Repetă pe ambele părți\n5. 3 repetări x 30 secunde cu 30s pauză'},

        # DUMINICĂ - Stretching și Core Ușor
        {'name': 'Cat-Cow Stretch', 'description': 'Stretching pentru coloană și core',
         'duration_seconds': 30, 'repetitions': 3, 'rest_seconds': 30, 'difficulty': 'beginner', 'muscle_group': 'core',
         'calories_burned': 9, 'day_of_week': 7,
         'instructions': '1. Poziție în patru labe\n2. Arcuiește spatele în sus (pisică)\n3. Arcuiește spatele în jos (vacă)\n4. Mișcare lentă și controlată\n5. 3 repetări x 30 secunde cu 30s pauză'},
    ]

    # Adaugă exercițiile în baza de date
    for exercise_data in exercises_data:
        exercise = Exercise(**exercise_data)
        db.session.add(exercise)

    # Creează utilizatorul admin implicit
    admin = User(username='admin', email='<EMAIL>', is_admin=True)
    admin.set_password('admin123')
    db.session.add(admin)

    # Creează un utilizator de test
    test_user = User(username='eddmanoo', email='<EMAIL>', is_admin=False)
    test_user.set_password('eddmanoo123')
    db.session.add(test_user)

    # Adaugă realizările predefinite
    if not Achievement.query.first():
        achievements_data = [
            {
                'name': 'Primul Pas',
                'description': 'Completează primul tău exercițiu!',
                'icon': 'fas fa-baby',
                'badge_color': 'success',
                'requirement_type': 'exercises',
                'requirement_value': 1,
                'points': 10
            },
            {
                'name': 'Războinic',
                'description': 'Completează 10 exerciții',
                'icon': 'fas fa-fist-raised',
                'badge_color': 'primary',
                'requirement_type': 'exercises',
                'requirement_value': 10,
                'points': 50
            },
            {
                'name': 'Maestru Fitness',
                'description': 'Completează 50 de exerciții',
                'icon': 'fas fa-crown',
                'badge_color': 'warning',
                'requirement_type': 'exercises',
                'requirement_value': 50,
                'points': 200
            },
            {
                'name': 'Arzător de Calorii',
                'description': 'Arde 100 de calorii',
                'icon': 'fas fa-fire',
                'badge_color': 'danger',
                'requirement_type': 'calories',
                'requirement_value': 100,
                'points': 30
            },
            {
                'name': 'Distrugător de Calorii',
                'description': 'Arde 500 de calorii',
                'icon': 'fas fa-fire-flame-curved',
                'badge_color': 'danger',
                'requirement_type': 'calories',
                'requirement_value': 500,
                'points': 100
            },
            {
                'name': 'Începător Consecvent',
                'description': 'Antrenează-te 3 zile consecutive',
                'icon': 'fas fa-calendar-check',
                'badge_color': 'info',
                'requirement_type': 'streak',
                'requirement_value': 3,
                'points': 25
            },
            {
                'name': 'Maestru Consecvent',
                'description': 'Antrenează-te 7 zile consecutive',
                'icon': 'fas fa-medal',
                'badge_color': 'warning',
                'requirement_type': 'streak',
                'requirement_value': 7,
                'points': 75
            },
            {
                'name': 'Legendă Consecvent',
                'description': 'Antrenează-te 30 de zile consecutive',
                'icon': 'fas fa-trophy',
                'badge_color': 'warning',
                'requirement_type': 'streak',
                'requirement_value': 30,
                'points': 300
            }
        ]

        for achievement_data in achievements_data:
            achievement = Achievement(**achievement_data)
            db.session.add(achievement)

    db.session.commit()
    print("Baza de date inițializată cu succes!")

# Actualizare durată exercițiu (AJAX)
@app.route('/update_exercise_duration', methods=['POST'])
@login_required
def update_exercise_duration():
    exercise_id = request.json.get('exercise_id')
    new_duration = request.json.get('duration')
    
    # Validare
    if not exercise_id or not new_duration:
        return jsonify({'success': False, 'message': 'Date incomplete'})
    
    if new_duration < 10 or new_duration > 120:
        return jsonify({'success': False, 'message': 'Durata trebuie să fie între 10 și 120 secunde'})
    
    # Actualizare exercițiu
    exercise = Exercise.query.get_or_404(exercise_id)
    
    # Salvăm durata veche pentru a recalcula caloriile
    old_duration = exercise.duration_seconds
    
    # Actualizăm durata
    exercise.duration_seconds = new_duration
    
    # Recalculăm caloriile în funcție de noua durată
    exercise.calories_burned = int(exercise.calories_burned * (new_duration / old_duration))
    
    db.session.commit()
    
    return jsonify({
        'success': True, 
        'message': f'Durata actualizată la {new_duration} secunde',
        'new_duration': new_duration,
        'new_calories': exercise.calories_burned,
        'new_total_duration': exercise.total_duration()
    })

@app.route('/admin/upload_exercise_images/<int:exercise_id>', methods=['GET', 'POST'])
@login_required
def upload_exercise_images(exercise_id):
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))
    
    exercise = Exercise.query.get_or_404(exercise_id)
    
    if request.method == 'POST':
        if 'files[]' not in request.files:
            flash('Nu s-au găsit fișiere!', 'error')
            return redirect(request.url)
        
        files = request.files.getlist('files[]')
        
        # Verifică dacă există cel puțin un fișier valid
        if not files or all(file.filename == '' for file in files):
            flash('Nu s-au selectat fișiere!', 'error')
            return redirect(request.url)
        
        # Procesează fiecare fișier
        uploaded_filenames = []
        
        for file in files:
            if file and allowed_file(file.filename):
                # Generează un nume unic pentru fișier
                filename = secure_filename(file.filename)
                unique_filename = f"{exercise_id}_{uuid.uuid4().hex}_{filename}"
                
                # Salvează fișierul
                file_path = os.path.join(exercise_images_dir, unique_filename)
                file.save(file_path)
                
                uploaded_filenames.append(unique_filename)
        
        # Actualizează lista de imagini în baza de date
        current_images = exercise.get_instruction_images()
        all_images = current_images + uploaded_filenames
        exercise.instruction_images = ','.join(all_images)
        
        db.session.commit()
        
        flash(f'S-au încărcat {len(uploaded_filenames)} imagini cu succes!', 'success')
        return redirect(url_for('admin'))
    
    return render_template('upload_images.html', exercise=exercise)

@app.route('/admin/delete_exercise_image/<int:exercise_id>/<path:image_name>', methods=['GET'])
@login_required
def delete_exercise_image(exercise_id, image_name):
    if not current_user.is_admin:
        flash('Nu ai permisiuni de administrator!', 'error')
        return redirect(url_for('dashboard'))
    
    exercise = Exercise.query.get_or_404(exercise_id)
    
    # Verifică dacă imaginea există în lista exercițiului
    current_images = exercise.get_instruction_images()
    if image_name in current_images:
        # Șterge fișierul
        file_path = os.path.join(exercise_images_dir, image_name)
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # Actualizează lista de imagini în baza de date
        current_images.remove(image_name)
        exercise.instruction_images = ','.join(current_images)
        db.session.commit()
        
        flash('Imaginea a fost ștearsă cu succes!', 'success')
    else:
        flash('Imaginea nu a fost găsită!', 'error')
    
    return redirect(url_for('upload_exercise_images', exercise_id=exercise_id))

if __name__ == '__main__':
    with app.app_context():
        init_database()
    app.run(debug=True, host='0.0.0.0', port=5000)
