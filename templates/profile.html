{% extends "base.html" %}

{% block title %}Profil - SlabIT{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user-circle"></i> Profilul meu
        </h1>
    </div>
</div>

<!-- Statistici rapide -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h4>{{ profile.total_points }}</h4>
                <p class="mb-0">Puncte totale</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-fire fa-2x mb-2"></i>
                <h4>{{ profile.streak_days }}</h4>
                <p class="mb-0">Zile consecutive</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-dumbbell fa-2x mb-2"></i>
                <h4>{{ total_sessions }}</h4>
                <p class="mb-0">Exerciții completate</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-burn fa-2x mb-2"></i>
                <h4>{{ total_calories }}</h4>
                <p class="mb-0">Calorii arse</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informații personale -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-user-edit"></i> Informații personale
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('update_profile') }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_weight" class="form-label">Greutatea actuală (kg)</label>
                                <input type="number" step="0.1" class="form-control" id="current_weight" 
                                       name="current_weight" value="{{ profile.current_weight or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target_weight" class="form-label">Greutatea țintă (kg)</label>
                                <input type="number" step="0.1" class="form-control" id="target_weight" 
                                       name="target_weight" value="{{ profile.target_weight or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="height_cm" class="form-label">Înălțimea (cm)</label>
                                <input type="number" class="form-control" id="height_cm" 
                                       name="height_cm" value="{{ profile.height_cm or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="age" class="form-label">Vârsta</label>
                                <input type="number" class="form-control" id="age" 
                                       name="age" value="{{ profile.age or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="gender" class="form-label">Sexul</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">Selectează...</option>
                                    <option value="male" {{ 'selected' if profile.gender == 'male' }}>Masculin</option>
                                    <option value="female" {{ 'selected' if profile.gender == 'female' }}>Feminin</option>
                                    <option value="other" {{ 'selected' if profile.gender == 'other' }}>Altul</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activity_level" class="form-label">Nivelul de activitate</label>
                                <select class="form-select" id="activity_level" name="activity_level">
                                    <option value="low" {{ 'selected' if profile.activity_level == 'low' }}>Scăzut</option>
                                    <option value="moderate" {{ 'selected' if profile.activity_level == 'moderate' }}>Moderat</option>
                                    <option value="high" {{ 'selected' if profile.activity_level == 'high' }}>Ridicat</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="daily_calorie_goal" class="form-label">Obiectiv zilnic de calorii</label>
                        <input type="number" class="form-control" id="daily_calorie_goal" 
                               name="daily_calorie_goal" value="{{ profile.daily_calorie_goal }}">
                        <div class="form-text">Câte calorii vrei să arzi zilnic prin exerciții</div>
                    </div>
                    
                    <!-- Informații calculate -->
                    {% if profile.calculate_bmi() %}
                    <div class="alert alert-info">
                        <strong>BMI:</strong> {{ profile.calculate_bmi() }} 
                        <span class="badge bg-{{ 'success' if profile.get_bmi_category() == 'Normal' else 'warning' if profile.get_bmi_category() in ['Subponderal', 'Supraponderal'] else 'danger' }}">
                            {{ profile.get_bmi_category() }}
                        </span>
                    </div>
                    {% endif %}
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvează modificările
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Realizări -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-medal"></i> Realizări
                    <span class="badge bg-primary">{{ achievements|length }}/{{ total_achievements }}</span>
                </h3>
            </div>
            <div class="card-body">
                {% if achievements %}
                    {% for user_achievement in achievements %}
                    <div class="achievement-item mb-3 p-3 border rounded">
                        <div class="d-flex align-items-center">
                            <div class="achievement-icon me-3">
                                <i class="{{ user_achievement.achievement.icon }} fa-2x text-{{ user_achievement.achievement.badge_color }}"></i>
                            </div>
                            <div class="achievement-info">
                                <h6 class="mb-1">{{ user_achievement.achievement.name }}</h6>
                                <p class="mb-1 text-muted small">{{ user_achievement.achievement.description }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-{{ user_achievement.achievement.badge_color }}">
                                        +{{ user_achievement.achievement.points }} puncte
                                    </span>
                                    <small class="text-muted">{{ user_achievement.earned_at.strftime('%d.%m.%Y') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-medal fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nu ai încă realizări.</p>
                        <p class="small">Completează primul exercițiu pentru a obține prima realizare!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}
.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
}
.achievement-item {
    transition: transform 0.2s ease-in-out;
}
.achievement-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
