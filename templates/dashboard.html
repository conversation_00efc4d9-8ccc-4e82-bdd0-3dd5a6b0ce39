{% extends "base.html" %}

{% block title %}Dashboard - SlabIT{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
            <small class="text-muted">- <PERSON><PERSON><PERSON> ziua, {{ current_user.username }}!</small>
        </h1>
    </div>
</div>

<!-- Progres săptămânal -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ weekly_progress.total_exercises if weekly_progress else 0 }}</h4>
                        <p class="mb-0">Exerciții completate</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dumbbell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ weekly_progress.total_calories if weekly_progress else 0 }}</h4>
                        <p class="mb-0">Calorii arse</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-fire fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ weekly_progress.total_time_minutes if weekly_progress else 0 }}</h4>
                        <p class="mb-0">Minute active</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ today_sessions|length }}</h4>
                        <p class="mb-0">Exerciții azi</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="d-flex flex-column h-100">
            <!-- Progres personal -->
            <div class="card mb-3 flex-grow-1">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-circle"></i> Progresul meu
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ user_profile.total_points }}</h4>
                                <small class="text-muted">Puncte</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ user_profile.streak_days }}</h4>
                            <small class="text-muted">Zile consecutive</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-user-edit"></i> Vezi profilul complet
                        </a>
                    </div>
                </div>
            </div>

            <!-- Realizări recente -->
            {% if recent_achievements %}
            <div class="card flex-grow-1">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy"></i> Realizări recente
                    </h5>
                </div>
                <div class="card-body">
                    {% for user_achievement in recent_achievements %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="{{ user_achievement.achievement.icon }} text-{{ user_achievement.achievement.badge_color }} me-2"></i>
                        <div class="flex-grow-1">
                            <small class="fw-bold">{{ user_achievement.achievement.name }}</small>
                            <div class="text-muted" style="font-size: 0.75rem;">
                                +{{ user_achievement.achievement.points }} puncte
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    <a href="{{ url_for('profile') }}" class="btn btn-outline-warning btn-sm w-100 mt-2">
                        <i class="fas fa-medal"></i> Vezi toate realizările
                    </a>
                </div>
            </div>
            {% else %}
            <!-- Card placeholder pentru realizări -->
            <div class="card flex-grow-1">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy"></i> Realizări
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-medal fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Nu ai încă realizări.</p>
                    <p class="small">Completează primul exercițiu pentru a obține prima realizare!</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-calendar-day"></i>
                    Exercițiile de azi
                    {% set days = ['Luni', 'Marți', 'Miercuri', 'Joi', 'Vineri', 'Sâmbătă', 'Duminică'] %}
                    <small class="text-muted">({{ days[today-1] }})</small>
                </h3>
            </div>
            <div class="card-body">
                {% if exercises %}
                    <div class="row">
                        {% for exercise in exercises %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 exercise-card" data-exercise-id="{{ exercise.id }}">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        {{ exercise.name }}
                                        <span class="badge bg-{{ 'success' if exercise.difficulty == 'beginner' else 'warning' if exercise.difficulty == 'intermediate' else 'danger' }}">
                                            {{ exercise.difficulty }}
                                        </span>
                                    </h5>
                                    <p class="card-text">{{ exercise.description }}</p>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <span class="duration-controls">
                                                <button class="btn btn-sm btn-outline-secondary decrease-duration" data-exercise-id="{{ exercise.id }}">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <span id="duration-{{ exercise.id }}">{{ exercise.duration_seconds }}</span>s
                                                <button class="btn btn-sm btn-outline-secondary increase-duration" data-exercise-id="{{ exercise.id }}">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </span> x {{ exercise.repetitions }} repetări |
                                            <i class="fas fa-pause"></i> {{ exercise.rest_seconds }}s pauză |
                                            <i class="fas fa-fire"></i> {{ exercise.calories_burned }} cal |
                                            <i class="fas fa-muscle"></i> {{ exercise.muscle_group }}
                                        </small>
                                    </div>

                                    <!-- Imagini cu instrucțiuni -->
                                    {% if exercise.get_instruction_images() %}
                                    <div class="instruction-carousel mb-3">
                                        <div id="carousel-{{ exercise.id }}" class="carousel slide" data-bs-ride="carousel">
                                            <div class="carousel-inner">
                                                {% for img in exercise.get_instruction_images() %}
                                                <div class="carousel-item {% if loop.first %}active{% endif %}">
                                                    <a href="{{ url_for('static', filename='images/exercises/' + img) }}" 
                                                       data-lightbox="exercise-{{ exercise.id }}" 
                                                       data-title="Pas {{ loop.index }} - {{ exercise.name }}">
                                                        <img src="{{ url_for('static', filename='images/exercises/' + img) }}" 
                                                             class="d-block w-100 rounded" style="max-height: 150px; object-fit: cover;"
                                                             alt="Instrucțiune {{ loop.index }} pentru {{ exercise.name }}">
                                                    </a>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% if exercise.get_instruction_images()|length > 1 %}
                                            <button class="carousel-control-prev" type="button" data-bs-target="#carousel-{{ exercise.id }}" data-bs-slide="prev">
                                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                <span class="visually-hidden">Anterior</span>
                                            </button>
                                            <button class="carousel-control-next" type="button" data-bs-target="#carousel-{{ exercise.id }}" data-bs-slide="next">
                                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                <span class="visually-hidden">Următor</span>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="text-center mb-3">
                                        <a href="{{ url_for('static', filename='images/exercises/default.jpg') }}" 
                                           data-lightbox="exercise-default-{{ exercise.id }}" 
                                           data-title="{{ exercise.name }} - Imagine generică">
                                            <img src="{{ url_for('static', filename='images/exercises/default.jpg') }}" 
                                                 class="img-fluid rounded" style="max-height: 150px;" 
                                                 alt="Imagine generică pentru {{ exercise.name }}">
                                        </a>
                                    </div>
                                    {% endif %}

                                    <!-- Timer Display -->
                                    <div class="timer-display text-center mb-3" style="display: none;">
                                        <div class="timer-circle">
                                            <span class="timer-text">{{ exercise.duration_seconds }}</span>
                                        </div>
                                        <div class="progress mt-2">
                                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary start-exercise"
                                                data-duration="{{ exercise.duration_seconds }}"
                                                data-repetitions="{{ exercise.repetitions }}"
                                                data-rest="{{ exercise.rest_seconds }}">
                                            <i class="fas fa-play"></i> Începe exercițiul ({{ exercise.repetitions }} x {{ exercise.duration_seconds }}s)
                                        </button>
                                        <button class="btn btn-outline-info" data-bs-toggle="collapse" data-bs-target="#instructions-{{ exercise.id }}">
                                            <i class="fas fa-info-circle"></i> Instrucțiuni
                                        </button>
                                    </div>

                                    <div class="collapse mt-3" id="instructions-{{ exercise.id }}">
                                        <div class="card card-body bg-light">
                                            <small>{{ exercise.instructions|replace('\n', '<br>')|safe }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h4>Nu sunt exerciții programate pentru azi</h4>
                        <p class="text-muted">Verifică exercițiile pentru alte zile din săptămână!</p>
                        <a href="{{ url_for('exercises_by_day', day=1) }}" class="btn btn-primary">
                            <i class="fas fa-calendar-week"></i> Vezi toate exercițiile
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Progresul recent -->
{% if today_sessions %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-history"></i> Exercițiile completate azi
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Exercițiu</th>
                                <th>Repetări</th>
                                <th>Durată</th>
                                <th>Calorii</th>
                                <th>Ora</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in today_sessions %}
                            <tr>
                                <td>{{ session.exercise.name }}</td>
                                <td>{{ session.repetitions_completed }}/{{ session.exercise.repetitions }}</td>
                                <td>{{ session.total_duration // 60 }}:{{ '%02d'|format(session.total_duration % 60) }}</td>
                                <td>{{ session.calories_burned }} cal</td>
                                <td>{{ session.completed_at.strftime('%H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/timer.js') }}"></script>
<!-- Adaugă lightbox pentru vizualizarea imaginilor -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
<script>
    // Configurare lightbox
    lightbox.option({
        'resizeDuration': 200,
        'wrapAround': true,
        'albumLabel': "Imagine %1 din %2",
        'fadeDuration': 300
    });
    
    // Inițializează toate caruselele
    document.addEventListener('DOMContentLoaded', function() {
        var carousels = document.querySelectorAll('.carousel');
        carousels.forEach(function(carousel) {
            new bootstrap.Carousel(carousel, {
                interval: 5000,
                touch: true
            });
        });
    });
</script>
{% endblock %}
