{% extends "base.html" %}

{% block title %}Admin Panel - SlabIT{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog"></i> <PERSON><PERSON> de Administrare
        </h1>
    </div>
</div>

<!-- Statistici generale -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|length }}</h4>
                        <p class="mb-0">Utilizatori totali</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ exercises|length }}</h4>
                        <p class="mb-0">Exerciții disponibile</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dumbbell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|selectattr('is_admin')|list|length }}</h4>
                        <p class="mb-0">Administratori</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gestionare utilizatori -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-users"></i> Gestionare Utilizatori
                    </h3>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-plus"></i> Adaugă utilizator
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nume utilizator</th>
                                <th>Email</th>
                                <th>Tip</th>
                                <th>Data creării</th>
                                <th>Acțiuni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>
                                    <i class="fas fa-user{{ '-shield' if user.is_admin else '' }}"></i>
                                    {{ user.username }}
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if user.is_admin else 'primary' }}">
                                        {{ 'Administrator' if user.is_admin else 'Utilizator' }}
                                    </span>
                                </td>
                                <td>{{ user.created_at.strftime('%d.%m.%Y %H:%M') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <!-- Buton editare -->
                                        <button class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editUserModal"
                                                onclick="populateEditModal({{ user.id }}, '{{ user.username }}', '{{ user.email }}', {{ user.is_admin|lower }})"
                                                title="Editează utilizatorul">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <!-- Buton backup -->
                                        <a href="{{ url_for('export_user_data', user_id=user.id) }}"
                                           class="btn btn-sm btn-outline-info"
                                           title="Descarcă backup date utilizator">
                                            <i class="fas fa-download"></i>
                                        </a>

                                        <!-- Buton ștergere (doar pentru alți utilizatori) -->
                                        {% if user.id != current_user.id %}
                                        <a href="{{ url_for('delete_user', user_id=user.id) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('⚠️ ATENȚIE!\n\nSigur vrei să ștergi utilizatorul {{ user.username }}?\n\nAceastă acțiune va șterge PERMANENT:\n• Toate exercițiile completate\n• Toate realizările obținute\n• Progresul săptămânal\n• Profilul personal\n• Toate datele asociate\n\nAceastă acțiune NU poate fi anulată!')"
                                           title="Șterge utilizatorul">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% else %}
                                        <span class="btn btn-sm btn-outline-secondary disabled">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exerciții disponibile -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-dumbbell"></i> Exerciții Disponibile
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nume</th>
                                <th>Zi</th>
                                <th>Format</th>
                                <th>Durată totală</th>
                                <th>Dificultate</th>
                                <th>Grup muscular</th>
                                <th>Calorii</th>
                                <th>Acțiuni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for exercise in exercises %}
                            <tr>
                                <td>{{ exercise.name }}</td>
                                <td>
                                    {% set days = ['Luni', 'Marți', 'Miercuri', 'Joi', 'Vineri', 'Sâmbătă', 'Duminică'] %}
                                    <span class="badge bg-secondary">{{ days[exercise.day_of_week-1] }}</span>
                                </td>
                                <td>
                                    <small>
                                        {{ exercise.repetitions }} x {{ exercise.duration_seconds }}s<br>
                                        <span class="text-muted">{{ exercise.rest_seconds }}s pauză</span>
                                    </small>
                                </td>
                                <td>{{ exercise.total_duration() // 60 }}:{{ '%02d'|format(exercise.total_duration() % 60) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if exercise.difficulty == 'beginner' else 'warning' if exercise.difficulty == 'intermediate' else 'danger' }}">
                                        {{ exercise.difficulty }}
                                    </span>
                                </td>
                                <td>{{ exercise.muscle_group }}</td>
                                <td>{{ exercise.calories_burned }} cal</td>
                                <td>
                                    <a href="{{ url_for('upload_exercise_images', exercise_id=exercise.id) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-images"></i> Imagini
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pentru adăugare utilizator -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Adaugă utilizator nou
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_user') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Nume utilizator</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Parolă</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                        <label class="form-check-label" for="is_admin">
                            Administrator
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvează
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal pentru editare utilizator -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i> Modifică utilizator
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Nume utilizator</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Parolă nouă</label>
                        <input type="password" class="form-control" id="edit_password" name="password"
                               placeholder="Lasă gol pentru a păstra parola actuală">
                        <div class="form-text">Lasă câmpul gol dacă nu vrei să schimbi parola</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_admin" name="is_admin">
                        <label class="form-check-label" for="edit_is_admin">
                            Administrator
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Actualizează
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function populateEditModal(userId, username, email, isAdmin) {
    // Setează action-ul formularului
    document.getElementById('editUserForm').action = '/admin/edit_user/' + userId;

    // Populează câmpurile
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_email').value = email;
    document.getElementById('edit_password').value = ''; // Resetează parola
    document.getElementById('edit_is_admin').checked = isAdmin;
}
</script>
{% endblock %}
