{% extends "base.html" %}

{% block title %}Încărcare Imagini - {{ exercise.name }} - EDDfit{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-images"></i> Încărcare Imagini pentru {{ exercise.name }}
            </h1>
            <a href="{{ url_for('admin') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la Admin
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Încarcă imagini noi</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_exercise_images', exercise_id=exercise.id) }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="files" class="form-label">Selectează imagini</label>
                        <input class="form-control" type="file" id="files" name="files[]" multiple accept="image/*">
                        <div class="form-text">Poți selecta mai multe imagini. Formatele acceptate: JPG, PNG, GIF.</div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Încarcă Imagini
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Imagini existente</h5>
            </div>
            <div class="card-body">
                {% if exercise.get_instruction_images() %}
                    <div class="row">
                        {% for img in exercise.get_instruction_images() %}
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <img src="{{ url_for('static', filename='images/exercises/' + img) }}" 
                                     class="card-img-top" alt="Instrucțiune {{ loop.index }}">
                                <div class="card-body p-2">
                                    <div class="d-grid">
                                        <a href="{{ url_for('delete_exercise_image', exercise_id=exercise.id, image_name=img) }}" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('Ești sigur că vrei să ștergi această imagine?')">
                                            <i class="fas fa-trash"></i> Șterge
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                        <p>Nu există imagini încărcate pentru acest exercițiu.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}