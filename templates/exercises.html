{% extends "base.html" %}

{% block title %}Exerciții {{ day_name }} - SlabIT{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-calendar-day"></i> Exerciții {{ day_name }}
            </h1>
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Navigare zile -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-calendar-week"></i> Navigar<PERSON> s<PERSON>
                </h5>
                <div class="btn-group w-100" role="group">
                    {% set days_list = [
                        (1, '<PERSON><PERSON>'),
                        (2, '<PERSON><PERSON><PERSON>'),
                        (3, '<PERSON><PERSON><PERSON><PERSON>'),
                        (4, '<PERSON><PERSON>'),
                        (5, 'Vineri'),
                        (6, 'Sâmbătă'),
                        (7, 'Duminică')
                    ] %}

                    {% for day_num, day_label in days_list %}
                    <a href="{{ url_for('exercises_by_day', day=day_num) }}"
                       class="btn {{ 'btn-primary' if day_num == day else 'btn-outline-primary' }}">
                        {{ day_label }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exercițiile zilei -->
<div class="row">
    <div class="col-12">
        {% if exercises %}
            <div class="row">
                {% for exercise in exercises %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 exercise-card" data-exercise-id="{{ exercise.id }}">
                        <div class="card-header">
                            <h5 class="mb-0">
                                {{ exercise.name }}
                                <span class="badge bg-{{ 'success' if exercise.difficulty == 'beginner' else 'warning' if exercise.difficulty == 'intermediate' else 'danger' }} float-end">
                                    {{ exercise.difficulty }}
                                </span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">{{ exercise.description }}</p>

                            <!-- Informații exercițiu -->
                            <div class="row text-center mb-3">
                                <div class="col-3">
                                    <div class="border rounded p-2 text-center">
                                        <i class="fas fa-clock text-primary"></i>
                                        <div class="d-flex align-items-center justify-content-center my-2">
                                            <button class="btn btn-sm btn-outline-secondary decrease-duration" data-exercise-id="{{ exercise.id }}">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <strong id="duration-{{ exercise.id }}" class="mx-2">{{ exercise.duration_seconds }}</strong>s
                                            <button class="btn btn-sm btn-outline-secondary increase-duration" data-exercise-id="{{ exercise.id }}">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">Per repetare</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <i class="fas fa-redo text-info"></i>
                                        <div><strong>{{ exercise.repetitions }}x</strong></div>
                                        <small class="text-muted">Repetări</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <i class="fas fa-fire text-danger"></i>
                                        <div><strong>{{ exercise.calories_burned }}</strong></div>
                                        <small class="text-muted">Calorii</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <i class="fas fa-stopwatch text-warning"></i>
                                        <div><strong>{{ exercise.total_duration() // 60 }}:{{ '%02d'|format(exercise.total_duration() % 60) }}</strong></div>
                                        <small class="text-muted">Total</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Timer Display -->
                            <div class="timer-display text-center mb-3" style="display: none;">
                                <div class="timer-circle mx-auto">
                                    <span class="timer-text">{{ exercise.duration_seconds }}</span>
                                </div>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="mt-2">
                                    <button class="btn btn-warning btn-sm pause-exercise">
                                        <i class="fas fa-pause"></i> Pauză
                                    </button>
                                    <button class="btn btn-danger btn-sm stop-exercise">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                </div>
                            </div>

                            <!-- Butoane acțiuni -->
                            <div class="d-grid gap-2">
                                <button class="btn btn-success start-exercise"
                                        data-duration="{{ exercise.duration_seconds }}"
                                        data-repetitions="{{ exercise.repetitions }}"
                                        data-rest="{{ exercise.rest_seconds }}">
                                    <i class="fas fa-play"></i> Începe exercițiul ({{ exercise.repetitions }} x {{ exercise.duration_seconds }}s)
                                </button>
                                <button class="btn btn-outline-info" data-bs-toggle="collapse" data-bs-target="#instructions-{{ exercise.id }}">
                                    <i class="fas fa-info-circle"></i> Vezi instrucțiunile
                                </button>
                            </div>
                        </div>

                        <!-- Instrucțiuni detaliate -->
                        <div class="collapse" id="instructions-{{ exercise.id }}">
                            <div class="card-footer bg-light">
                                <h6><i class="fas fa-list-ol"></i> Instrucțiuni:</h6>
                                <div class="instructions-text">
                                    {{ exercise.instructions|replace('\n', '<br>')|safe }}
                                </div>
                                
                                <!-- Imagini cu instrucțiuni -->
                                {% if exercise.get_instruction_images() %}
                                <div class="instruction-images mt-3">
                                    <div class="row">
                                        {% for img in exercise.get_instruction_images() %}
                                        <div class="col-md-4 mb-2">
                                            <a href="{{ url_for('static', filename='images/exercises/' + img) }}" 
                                               data-lightbox="exercise-{{ exercise.id }}" 
                                               data-title="Pas {{ loop.index }} - {{ exercise.name }}">
                                                <img src="{{ url_for('static', filename='images/exercises/' + img) }}" 
                                                     class="img-fluid rounded" 
                                                     alt="Instrucțiune {{ loop.index }} pentru {{ exercise.name }}">
                                            </a>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="text-center mt-3">
                                    <a href="{{ url_for('static', filename='images/exercises/default.jpg') }}" 
                                       data-lightbox="exercise-default-{{ exercise.id }}" 
                                       data-title="{{ exercise.name }} - Imagine generică">
                                        <img src="{{ url_for('static', filename='images/exercises/default.jpg') }}" 
                                             class="img-fluid rounded" style="max-height: 200px;" 
                                             alt="Imagine generică pentru {{ exercise.name }}">
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                <h3>Nu sunt exerciții programate pentru {{ day_name }}</h3>
                <p class="text-muted">Încearcă o altă zi din săptămână!</p>

                <div class="mt-4">
                    {% if day > 1 %}
                    <a href="{{ url_for('exercises_by_day', day=day-1) }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-chevron-left"></i> Ziua anterioară
                    </a>
                    {% endif %}

                    {% if day < 7 %}
                    <a href="{{ url_for('exercises_by_day', day=day+1) }}" class="btn btn-outline-primary">
                        Ziua următoare <i class="fas fa-chevron-right"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal pentru felicitări -->
<div class="modal fade" id="congratsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-trophy"></i> Felicitări!
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-medal fa-3x text-warning mb-3"></i>
                <h4>Exercițiu completat!</h4>
                <p class="congrats-message">Ai completat cu succes exercițiul!</p>
                <div class="calories-info">
                    <span class="badge bg-danger fs-6">
                        <i class="fas fa-fire"></i> <span class="calories-burned">0</span> calorii arse
                    </span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                    <i class="fas fa-thumbs-up"></i> Continuă
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/timer.js') }}"></script>
{% endblock %}
