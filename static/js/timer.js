// Timer functionality pentru exerciții cu repetări și pauze
class ExerciseTimer {
    constructor() {
        this.currentTimer = null;
        this.isPaused = false;
        this.timeRemaining = 0;
        this.currentExerciseId = null;
        this.currentCard = null;

        // Parametrii exercițiului
        this.exerciseDuration = 30;  // secunde per repetare
        this.totalRepetitions = 3;   // numărul total de repetări
        this.restDuration = 30;      // secunde de pauză

        // Starea curentă
        this.currentRepetition = 0;  // repetarea curentă (0-based)
        this.isResting = false;      // dacă suntem în pauză
        this.totalTimeElapsed = 0;   // timpul total scurs

        this.initializeEventListeners();
        this.initializeDurationControls(); // Adaugă inițializarea controalelor de durată
    }

    initializeEventListeners() {
        // Event listeners pentru butoanele de start
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('start-exercise') ||
                e.target.closest('.start-exercise')) {
                e.preventDefault();
                this.handleStartExercise(e);
            }

            if (e.target.classList.contains('pause-exercise') ||
                e.target.closest('.pause-exercise')) {
                e.preventDefault();
                this.pauseTimer();
            }

            if (e.target.classList.contains('stop-exercise') ||
                e.target.closest('.stop-exercise')) {
                e.preventDefault();
                this.stopTimer();
            }
        });

        // Sunet pentru notificări (dacă este suportat)
        this.audioContext = null;
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Audio context not supported');
        }
    }

    handleStartExercise(e) {
        const button = e.target.closest('.start-exercise');
        const card = button.closest('.exercise-card');
        const duration = parseInt(button.dataset.duration);
        const repetitions = parseInt(button.dataset.repetitions);
        const rest = parseInt(button.dataset.rest);
        const exerciseId = card.dataset.exerciseId;

        // Oprește timer-ul curent dacă există
        if (this.currentTimer) {
            this.stopTimer();
        }

        this.startExercise(card, exerciseId, duration, repetitions, rest);
    }

    startExercise(card, exerciseId, duration, repetitions, rest) {
        this.currentCard = card;
        this.currentExerciseId = exerciseId;
        this.exerciseDuration = duration;
        this.totalRepetitions = repetitions;
        this.restDuration = rest;

        // Resetează starea
        this.currentRepetition = 0;
        this.isResting = false;
        this.totalTimeElapsed = 0;
        this.isPaused = false;

        // Începe prima repetare
        this.startRepetition();

        // Adaugă clasa active la card
        card.classList.add('active');

        // Scroll la exercițiul activ
        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    startRepetition() {
        this.timeRemaining = this.isResting ? this.restDuration : this.exerciseDuration;

        // Actualizează UI
        this.updateUI();

        // Pornește timer-ul
        this.currentTimer = setInterval(() => {
            if (!this.isPaused) {
                this.timeRemaining--;
                this.totalTimeElapsed++;
                this.updateTimerDisplay();

                if (this.timeRemaining <= 0) {
                    this.handleRepetitionComplete();
                }
            }
        }, 1000);
    }

    handleRepetitionComplete() {
        clearInterval(this.currentTimer);

        if (this.isResting) {
            // Sfârșitul pauzei - începe următoarea repetare
            this.isResting = false;
            this.currentRepetition++;

            if (this.currentRepetition < this.totalRepetitions) {
                // Mai sunt repetări - începe următoarea
                this.playBeep();
                setTimeout(() => {
                    this.startRepetition();
                }, 1000);
            } else {
                // Toate repetările completate
                this.completeExercise();
            }
        } else {
            // Sfârșitul unei repetări de exercițiu
            this.playBeep();

            if (this.currentRepetition + 1 < this.totalRepetitions) {
                // Mai sunt repetări - începe pauza
                this.isResting = true;
                setTimeout(() => {
                    this.startRepetition();
                }, 1000);
            } else {
                // Ultima repetare completată
                this.currentRepetition++;
                this.completeExercise();
            }
        }
    }

    updateUI() {
        const startButton = this.currentCard.querySelector('.start-exercise');
        const timerDisplay = this.currentCard.querySelector('.timer-display');
        const timerText = this.currentCard.querySelector('.timer-text');
        const progressBar = this.currentCard.querySelector('.progress-bar');
        const timerCircle = this.currentCard.querySelector('.timer-circle');

        // Ascunde butonul de start și arată timer-ul
        startButton.style.display = 'none';
        timerDisplay.style.display = 'block';

        // Resetează timer-ul
        timerText.textContent = this.timeRemaining;
        progressBar.style.width = '0%';
        timerCircle.classList.remove('paused', 'completed');
    }

    updateTimerDisplay() {
        const timerText = this.currentCard.querySelector('.timer-text');
        const progressBar = this.currentCard.querySelector('.progress-bar');

        // Actualizează textul cu informații despre repetări
        const statusText = this.isResting ?
            `PAUZĂ ${this.timeRemaining}s` :
            `${this.timeRemaining}s`;
        const repetitionText = `${this.currentRepetition + 1}/${this.totalRepetitions}`;

        timerText.innerHTML = `${statusText}<br><small>${repetitionText}</small>`;

        // Calculează progresul pentru repetarea curentă
        const currentDuration = this.isResting ? this.restDuration : this.exerciseDuration;
        const currentProgress = ((currentDuration - this.timeRemaining) / currentDuration) * 100;
        progressBar.style.width = currentProgress + '%';

        // Schimbă culoarea în funcție de stare
        if (this.isResting) {
            progressBar.className = 'progress-bar bg-info';  // Albastru pentru pauză
        } else {
            if (currentProgress < 50) {
                progressBar.className = 'progress-bar bg-success';
            } else if (currentProgress < 80) {
                progressBar.className = 'progress-bar bg-warning';
            } else {
                progressBar.className = 'progress-bar bg-danger';
            }
        }

        // Efecte vizuale pentru ultimele 3 secunde
        if (this.timeRemaining <= 3 && this.timeRemaining > 0) {
            this.currentCard.classList.add('bounce');
            setTimeout(() => {
                this.currentCard.classList.remove('bounce');
            }, 1000);

            // Sunet de avertizare
            if (this.timeRemaining === 3) {
                this.playBeep();
            }
        }
    }

    pauseTimer() {
        this.isPaused = !this.isPaused;
        const pauseButton = this.currentCard.querySelector('.pause-exercise');
        const timerCircle = this.currentCard.querySelector('.timer-circle');

        if (this.isPaused) {
            pauseButton.innerHTML = '<i class="fas fa-play"></i> Continuă';
            timerCircle.classList.add('paused');
        } else {
            pauseButton.innerHTML = '<i class="fas fa-pause"></i> Pauză';
            timerCircle.classList.remove('paused');
        }
    }

    stopTimer() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
            this.currentTimer = null;
        }

        if (this.currentCard) {
            this.resetCardUI();
        }

        this.currentCard = null;
        this.currentExerciseId = null;
        this.isPaused = false;
    }

    resetCardUI() {
        const startButton = this.currentCard.querySelector('.start-exercise');
        const timerDisplay = this.currentCard.querySelector('.timer-display');
        const pauseButton = this.currentCard.querySelector('.pause-exercise');

        // Resetează UI
        startButton.style.display = 'block';
        timerDisplay.style.display = 'none';
        pauseButton.innerHTML = '<i class="fas fa-pause"></i> Pauză';

        // Elimină clasele active
        this.currentCard.classList.remove('active');

        const timerCircle = this.currentCard.querySelector('.timer-circle');
        timerCircle.classList.remove('paused', 'completed');
    }

    completeExercise() {
        // Oprește timer-ul
        clearInterval(this.currentTimer);
        this.currentTimer = null;

        // Efecte vizuale de completare
        const timerCircle = this.currentCard.querySelector('.timer-circle');
        timerCircle.classList.add('completed');

        // Sunet de succes
        this.playSuccessSound();

        // Salvează progresul în backend
        this.saveProgress();

        // Resetează UI după o scurtă întârziere
        setTimeout(() => {
            this.resetCardUI();
        }, 2000);
    }

    saveProgress() {
        fetch('/complete_exercise', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exercise_id: this.currentExerciseId,
                repetitions_completed: this.currentRepetition,
                total_duration: this.totalTimeElapsed
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showCongratulations(data.message, data.calories, data.repetitions, data.total_repetitions);

                // Afișează realizările noi
                if (data.new_achievements && data.new_achievements.length > 0) {
                    setTimeout(() => {
                        this.showNewAchievements(data.new_achievements);
                    }, 2000);
                }
            } else {
                console.error('Eroare la salvarea progresului:', data.message);
            }
        })
        .catch(error => {
            console.error('Eroare de rețea:', error);
        });
    }

    showCongratulations(message, calories, repetitions, totalRepetitions) {
        // Actualizează modal-ul de felicitări
        const modal = document.getElementById('congratsModal');
        if (modal) {
            const messageElement = modal.querySelector('.congrats-message');
            const caloriesElement = modal.querySelector('.calories-burned');

            if (messageElement) messageElement.textContent = message;
            if (caloriesElement) caloriesElement.textContent = calories;

            // Arată modal-ul
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } else {
            // Fallback - arată o alertă simplă
            alert(`${message}\nAi ars ${calories} calorii!`);
        }

        // Actualizează statisticile pe pagină (dacă există)
        this.updatePageStats(calories);
    }

    showNewAchievements(achievements) {
        achievements.forEach((achievement, index) => {
            setTimeout(() => {
                // Creează notificarea
                const notification = document.createElement('div');
                notification.className = 'achievement-notification';
                notification.innerHTML = `
                    <div class="achievement-popup">
                        <div class="achievement-header">
                            <i class="${achievement.icon} fa-2x text-warning"></i>
                            <h4>Realizare nouă!</h4>
                        </div>
                        <div class="achievement-body">
                            <h5>${achievement.name}</h5>
                            <p>${achievement.description}</p>
                            <span class="badge bg-success">+${achievement.points} puncte</span>
                        </div>
                    </div>
                `;

                // Adaugă stiluri
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
                    border-radius: 15px;
                    padding: 20px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    transform: translateX(400px);
                    transition: transform 0.5s ease-in-out;
                    max-width: 300px;
                    color: #333;
                `;

                document.body.appendChild(notification);

                // Animație de intrare
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                // Elimină după 5 secunde
                setTimeout(() => {
                    notification.style.transform = 'translateX(400px)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 500);
                }, 5000);

                // Sunet de realizare
                this.playAchievementSound();

            }, index * 1000); // Întârziere între realizări multiple
        });
    }

    updatePageStats(calories) {
        // Actualizează contorul de calorii de pe pagină
        const caloriesCounter = document.querySelector('.total-calories');
        if (caloriesCounter) {
            const currentCalories = parseInt(caloriesCounter.textContent) || 0;
            caloriesCounter.textContent = currentCalories + calories;
        }

        // Actualizează contorul de exerciții
        const exercisesCounter = document.querySelector('.total-exercises');
        if (exercisesCounter) {
            const currentExercises = parseInt(exercisesCounter.textContent) || 0;
            exercisesCounter.textContent = currentExercises + 1;
        }
    }

    playBeep() {
        if (this.audioContext) {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.1);
        }
    }

    playSuccessSound() {
        if (this.audioContext) {
            // Sunet de succes mai complex
            const frequencies = [523.25, 659.25, 783.99]; // Do, Mi, Sol

            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    oscillator.frequency.value = freq;
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);

                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + 0.3);
                }, index * 100);
            });
        }
    }

    playAchievementSound() {
        if (this.audioContext) {
            // Sunet special pentru realizări - acorduri majore
            const frequencies = [261.63, 329.63, 392.00, 523.25]; // Do, Mi, Sol, Do înalt

            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    oscillator.frequency.value = freq;
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0.15, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.8);

                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + 0.8);
                }, index * 200);
            });
        }
    }

    // Gestionare butoane de ajustare a duratei
    initializeDurationControls() {
        // Butoane de scădere a duratei
        document.querySelectorAll('.decrease-duration').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const exerciseId = button.dataset.exerciseId;
                this.updateExerciseDuration(exerciseId, -5); // Scade cu 5 secunde
            });
        });
        
        // Butoane de creștere a duratei
        document.querySelectorAll('.increase-duration').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const exerciseId = button.dataset.exerciseId;
                this.updateExerciseDuration(exerciseId, 5); // Crește cu 5 secunde
            });
        });
    }

    // Funcție pentru actualizarea duratei exercițiului
    updateExerciseDuration(exerciseId, change) {
        const durationElement = document.getElementById(`duration-${exerciseId}`);
        let currentDuration = parseInt(durationElement.textContent);
        let newDuration = currentDuration + change;
        
        // Validare: durata minimă 10s, maximă 120s
        if (newDuration < 10) newDuration = 10;
        if (newDuration > 120) newDuration = 120;
        
        // Dacă durata nu s-a schimbat, nu facem nimic
        if (newDuration === currentDuration) return;
        
        // Trimite cerere AJAX pentru actualizare
        fetch('/update_exercise_duration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exercise_id: exerciseId,
                duration: newDuration
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Actualizează interfața
                durationElement.textContent = data.new_duration;
                
                // Actualizează butonul de start
                const startButton = document.querySelector(`.exercise-card[data-exercise-id="${exerciseId}"] .start-exercise`);
                startButton.dataset.duration = data.new_duration;
                startButton.innerHTML = `<i class="fas fa-play"></i> Începe exercițiul (${startButton.dataset.repetitions} x ${data.new_duration}s)`;
                
                // Actualizează afișarea caloriilor
                const caloriesElement = document.querySelector(`.exercise-card[data-exercise-id="${exerciseId}"] .card-text small i.fas.fa-fire`).nextSibling;
                caloriesElement.textContent = ` ${data.new_calories} cal`;
                
                // Actualizează durata totală
                const totalDurationElement = document.querySelector(`.exercise-card[data-exercise-id="${exerciseId}"] .badge.bg-info i.fas.fa-stopwatch`).nextSibling;
                const minutes = Math.floor(data.new_total_duration / 60);
                const seconds = data.new_total_duration % 60;
                totalDurationElement.textContent = ` Total: ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                
                // Notificare opțională
                this.showToast(data.message);
            } else {
                console.error('Eroare la actualizarea duratei:', data.message);
            }
        })
        .catch(error => {
            console.error('Eroare de rețea:', error);
        });
    }

    // Metodă pentru afișarea unui toast de notificare
    showToast(message) {
        // Implementează un toast simplu sau folosește un sistem existent
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // Animație de afișare
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Dispariție automată
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// Inițializează timer-ul când se încarcă pagina
document.addEventListener('DOMContentLoaded', function() {
    new ExerciseTimer();

    // Adaugă efecte de fade-in pentru carduri
    const cards = document.querySelectorAll('.exercise-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
});
