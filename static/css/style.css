/* Custom styles pentru SlabIT */

/* General */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Exercise Cards */
.exercise-card {
    position: relative;
    overflow: hidden;
}

.exercise-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

.exercise-card.active {
    border: 3px solid #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

/* Timer Styles */
.timer-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 5px solid #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.timer-text {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

/* Progress Bar Animation */
.progress-bar {
    transition: width 0.1s linear;
}

/* Button Styles */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.start-exercise {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.start-exercise:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
}

.start-exercise:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Stats Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info,
.card.bg-danger {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

/* Badges */
.badge {
    border-radius: 15px;
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* Instructions */
.instructions-text {
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #495057;
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Pulse Animation for Active Exercise */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.exercise-card.active {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .timer-circle {
        width: 100px;
        height: 100px;
    }

    .timer-text {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Success Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.bounce {
    animation: bounce 1s ease-in-out;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Card alignment improvements */
.h-100 {
    height: 100% !important;
}

.d-flex.flex-column.h-100 {
    min-height: 100%;
}

.flex-grow-1 {
    flex-grow: 1 !important;
}

/* Ensure equal height cards in dashboard */
.row.mb-4 .col-md-8,
.row.mb-4 .col-md-4 {
    display: flex;
    flex-direction: column;
}

.row.mb-4 .card {
    flex: 1;
}

/* Progress section styling */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
}

/* Stiluri pentru controalele de durată */
.duration-controls {
    display: inline-flex;
    align-items: center;
    margin: 0 5px;
}

.duration-controls button {
    padding: 0 5px;
    font-size: 0.7rem;
    line-height: 1;
    border-radius: 3px;
    margin: 0 3px;
}

/* Aliniere butoane în cardul de exerciții */
.border.rounded .btn-sm {
    padding: 0.25rem 0.5rem;
    vertical-align: middle;
    line-height: 1;
}

.border.rounded strong {
    display: inline-block;
    min-width: 25px;
    text-align: center;
    vertical-align: middle;
}

/* Îmbunătățiri pentru containerul de durată */
.border.rounded p-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.border.rounded div {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 5px 0;
}

/* Stiluri pentru notificări toast */
.toast-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 9999;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.toast-notification.show {
    opacity: 1;
    transform: translateY(0);
}

/* Stiluri pentru caruselul de imagini */
.instruction-carousel .carousel-item {
    height: 150px;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.instruction-carousel .carousel-item img {
    height: 150px;
    object-fit: cover;
    border-radius: 0.25rem;
}

.instruction-carousel .carousel-control-prev,
.instruction-carousel .carousel-control-next {
    width: 10%;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 0.25rem;
}

.instruction-carousel .carousel-control-prev:hover,
.instruction-carousel .carousel-control-next:hover {
    background-color: rgba(0, 0, 0, 0.4);
}
