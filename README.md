# 🏋️‍♀️ EDDfit - Aplicația ta de slăbire prin exerciții acasă

**EDDfit** este o aplicație web modernă și interactivă, concepută special pentru a te ajuta să slăbești prin exerciții fizice pe care le poți face a<PERSON><PERSON>, cu focus pe zona abdominală. Aplicația oferă un sistem complet de antrenament cu cronometru integrat, urmărire progres și sistem de realizări pentru a te motiva să îți atingi obiectivele.

## 🎯 Caracteristici principale

### 💪 Sistem de exerciții optimizat
- **30 secunde** per repetare
- **3 repetări** per exercițiu
- **30 secunde** pauză între repetări
- **Exerciții zilnice** personalizate pentru fiecare zi din săptămână
- **Focus pe abdomen** - exerciții specifice pentru slăbire rapidă

### ⏱️ Cronometru inteligent
- **Timer interactiv** cu afișare repetări (1/3, 2/3, 3/3)
- **Diferențiere vizuală** între exercițiu și pauză
- **Progress bar animat** cu culori diferite
- **Efecte sonore** pentru notificări și finalizare
- **Butoane de control**: Start, Pauză, Stop

### 🏆 Sistem de realizări și motivație
- **8 realizări predefinite** pentru diferite obiective
- **Sistem de puncte** pentru gamificare
- **Streak counter** - zile consecutive de antrenament
- **Notificări animate** pentru realizări noi
- **Progres vizual** în timp real

### 👤 Profil personal complet
- **Informații personale**: greutate, înălțime, vârstă
- **Calculare automată BMI** cu categorii
- **Obiective personalizate** de calorii
- **Statistici detaliate** de progres
- **Istoric complet** al antrenamentelor

### 👥 Administrare utilizatori
- **Panou admin** pentru gestionarea utilizatorilor
- **CRUD complet**: Adăugare, editare, ștergere utilizatori
- **Export date** pentru backup
- **Roluri diferite**: Admin și utilizator normal

## 🚀 Instalare și configurare

### Cerințe de sistem
- Python 3.8 sau mai nou
- Pip (Python package manager)
- Browser web modern

### 1. Clonează repository-ul
```bash
git clone https://github.com/username/slabit.git
cd slabit
```

### 2. Instalează dependențele
```bash
# Instalează Flask și extensiile necesare
pip install Flask Flask-SQLAlchemy Flask-Login Werkzeug python-dotenv

# SAU folosește fișierul requirements.txt
pip install -r requirements.txt
```

### 3. Pornește aplicația
```bash
python app.py
```

### 4. Accesează aplicația
Deschide browser-ul și navighează la: `http://localhost:5000`

## 🎮 Cum să folosești aplicația

### 🔐 Autentificare
**Cont demo disponibil:**
- **Utilizator**: `admin`
- **Parolă**: `admin123`

### 📊 Dashboard principal
1. **Vezi exercițiile de azi** - programate automat pentru ziua curentă
2. **Monitorizează progresul** - puncte, streak, statistici
3. **Explorează realizările** - badge-uri obținute recent

### 🏃‍♀️ Completarea exercițiilor
1. **Selectează un exercițiu** din lista zilnică
2. **Citește instrucțiunile** pentru execuția corectă
3. **Apasă "Începe exercițiul"** pentru a porni cronometrul
4. **Urmărește timer-ul** și repetările (1/3, 2/3, 3/3)
5. **Respectă pauzele** de 30 secunde între repetări
6. **Primește realizări** automat la completare

### 📅 Exerciții săptămânale
- **Luni**: Plank, Crunches (abdomen superior)
- **Marți**: Mountain Climbers, Russian Twists (cardio + core)
- **Miercuri**: Leg Raises, Dead Bug (abdomen inferior)
- **Joi**: Burpees, High Knees (cardio intens)
- **Vineri**: Bicycle Crunches, Plank to Push-up (combinat)
- **Sâmbătă**: Side Plank (recuperare activă)
- **Duminică**: Cat-Cow Stretch (stretching)

### 👤 Gestionarea profilului
1. **Accesează profilul** din meniul utilizator
2. **Completează informațiile personale** (greutate, înălțime, etc.)
3. **Setează obiective** de calorii zilnice
4. **Monitorizează BMI-ul** calculat automat
5. **Explorează realizările** obținute

### 🛡️ Funcții admin
**Doar pentru administratori:**
1. **Gestionează utilizatori** - adaugă, editează, șterge
2. **Exportă date** pentru backup
3. **Monitorizează statistici** generale
4. **Vizualizează toate exercițiile** disponibile

## 🏆 Sistemul de realizări

### 🥇 Realizări disponibile:
- 🍼 **Primul Pas** - Primul exercițiu (10 puncte)
- ✊ **Războinic** - 10 exerciții (50 puncte)
- 👑 **Maestru Fitness** - 50 exerciții (200 puncte)
- 🔥 **Arzător de Calorii** - 100 calorii (30 puncte)
- 🔥 **Distrugător de Calorii** - 500 calorii (100 puncte)
- 📅 **Începător Consecvent** - 3 zile consecutive (25 puncte)
- 🏅 **Maestru Consecvent** - 7 zile consecutive (75 puncte)
- 🏆 **Legendă Consecvent** - 30 zile consecutive (300 puncte)

## 🛠️ Tehnologii folosite

### Backend
- **Flask** - Framework web Python
- **SQLAlchemy** - ORM pentru baza de date
- **Flask-Login** - Gestionarea sesiunilor
- **SQLite** - Baza de date

### Frontend
- **HTML5/CSS3** - Structură și stilizare
- **Bootstrap 5** - Framework CSS responsive
- **JavaScript** - Interactivitate și cronometru
- **Font Awesome** - Iconuri

### Funcționalități avansate
- **Timer JavaScript** cu repetări și pauze
- **Notificări animate** pentru realizări
- **Audio feedback** pentru cronometru
- **Export JSON** pentru backup date
- **Responsive design** pentru mobile

## 📁 Structura proiectului

```
eddfit/
├── app.py                 # Aplicația principală Flask
├── models.py              # Modele bază de date
├── requirements.txt       # Dependențe Python
├── eddfit.db              # Baza de date SQLite
├── templates/            # Template-uri HTML
│   ├── base.html         # Template de bază
│   ├── login.html        # Pagina de login
│   ├── dashboard.html    # Dashboard principal
│   ├── exercises.html    # Exerciții pe zile
│   ├── profile.html      # Profil utilizator
│   └── admin.html        # Panou admin
├── static/               # Fișiere statice
│   ├── css/
│   │   └── style.css     # Stiluri personalizate
│   └── js/
│       └── timer.js      # Logica cronometrului
└── README.md             # Documentația proiectului
```

## 🎨 Capturi de ecran

### Dashboard principal
- Exercițiile zilnice cu cronometru integrat
- Progresul personal și realizări
- Statistici în timp real

### Cronometru interactiv
- Timer cu repetări și pauze
- Progress bar animat
- Efecte vizuale și sonore

### Profil personal
- Informații complete despre utilizator
- Calculare automată BMI
- Istoric realizări și progres

## 🤝 Contribuții

Contribuțiile sunt binevenite! Pentru a contribui:

1. Fork repository-ul
2. Creează o ramură pentru feature-ul tău
3. Commit modificările
4. Push în ramura ta
5. Deschide un Pull Request

## 📄 Licență

Acest proiect este licențiat sub MIT License - vezi fișierul [LICENSE](LICENSE) pentru detalii.

## 📞 Contact

Pentru întrebări sau sugestii, te rugăm să deschizi un issue pe GitHub.

---

**EDDfit** - Transformă-ți corpul acasă, cu exerciții simple și eficiente! 💪✨
