from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.<PERSON><PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relația cu progresul exercițiilor (ștergere în cascadă)
    exercise_sessions = db.relationship('ExerciseSession', backref='user', lazy=True, cascade='all, delete-orphan')
    weekly_progress = db.relationship('WeeklyProgress', backref='user', lazy=True, cascade='all, delete-orphan')
    user_achievements = db.relationship('UserAchievement', backref='user', lazy=True, cascade='all, delete-orphan')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Exercise(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    duration_seconds = db.Column(db.Integer, nullable=False, default=30)  # durata unei repetări în secunde
    repetitions = db.Column(db.Integer, nullable=False, default=3)  # numărul de repetări
    rest_seconds = db.Column(db.Integer, nullable=False, default=30)  # pauza între repetări în secunde
    difficulty = db.Column(db.String(20), nullable=False)  # beginner, intermediate, advanced
    muscle_group = db.Column(db.String(50), nullable=False)  # abdomen, core, cardio
    calories_burned = db.Column(db.Integer, nullable=False)  # calorii estimate pe exercițiu complet
    instructions = db.Column(db.Text, nullable=False)
    instruction_images = db.Column(db.Text, nullable=True)  # Stochează liste de imagini separate prin virgulă
    day_of_week = db.Column(db.Integer, nullable=False)  # 1=Luni, 7=Duminică

    def total_duration(self):
        """Calculează durata totală a exercițiului (repetări + pauze)"""
        return (self.duration_seconds * self.repetitions) + (self.rest_seconds * (self.repetitions - 1))

    def update_calories_for_duration(self, new_duration):
        """Recalculează caloriile în funcție de noua durată"""
        calories_per_second = self.calories_burned / self.duration_seconds
        return int(calories_per_second * new_duration)

    def get_instruction_images(self):
        if not self.instruction_images:
            return []
        return self.instruction_images.split(',')

    def __repr__(self):
        return f'<Exercise {self.name}>'

class ExerciseSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    exercise_id = db.Column(db.Integer, db.ForeignKey('exercise.id'), nullable=False)
    completed_at = db.Column(db.DateTime, default=datetime.utcnow)
    repetitions_completed = db.Column(db.Integer, nullable=False, default=0)  # repetări completate
    total_duration = db.Column(db.Integer, nullable=False)  # durata totală în secunde
    calories_burned = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)

    # Relații
    exercise = db.relationship('Exercise', backref='sessions')

    def __repr__(self):
        return f'<ExerciseSession {self.user_id}-{self.exercise_id}>'

class WeeklyProgress(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    week_start = db.Column(db.Date, nullable=False)
    total_exercises = db.Column(db.Integer, default=0)
    total_calories = db.Column(db.Integer, default=0)
    total_time_minutes = db.Column(db.Integer, default=0)
    weight_kg = db.Column(db.Float)  # greutatea utilizatorului (opțional)

    def __repr__(self):
        return f'<WeeklyProgress {self.user_id}-{self.week_start}>'

class Achievement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    icon = db.Column(db.String(50), nullable=False)  # Font Awesome icon class
    badge_color = db.Column(db.String(20), default='primary')  # Bootstrap color
    requirement_type = db.Column(db.String(50), nullable=False)  # exercises, calories, streak, etc.
    requirement_value = db.Column(db.Integer, nullable=False)
    points = db.Column(db.Integer, default=10)

    def __repr__(self):
        return f'<Achievement {self.name}>'

class UserAchievement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    achievement_id = db.Column(db.Integer, db.ForeignKey('achievement.id'), nullable=False)
    earned_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relații
    achievement = db.relationship('Achievement', backref='earned_by')

    def __repr__(self):
        return f'<UserAchievement {self.user_id}-{self.achievement_id}>'

class UserProfile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    current_weight = db.Column(db.Float)
    target_weight = db.Column(db.Float)
    height_cm = db.Column(db.Integer)
    age = db.Column(db.Integer)
    gender = db.Column(db.String(10))  # male, female, other
    activity_level = db.Column(db.String(20), default='moderate')  # low, moderate, high
    daily_calorie_goal = db.Column(db.Integer, default=200)
    streak_days = db.Column(db.Integer, default=0)
    total_points = db.Column(db.Integer, default=0)
    last_workout_date = db.Column(db.Date)

    # Relație
    user = db.relationship('User', backref=db.backref('profile', uselist=False))

    def calculate_bmi(self):
        if self.current_weight and self.height_cm:
            height_m = self.height_cm / 100
            return round(self.current_weight / (height_m ** 2), 1)
        return None

    def get_bmi_category(self):
        bmi = self.calculate_bmi()
        if not bmi:
            return "Necunoscut"
        if bmi < 18.5:
            return "Subponderal"
        elif bmi < 25:
            return "Normal"
        elif bmi < 30:
            return "Supraponderal"
        else:
            return "Obez"

    def __repr__(self):
        return f'<UserProfile {self.user_id}>'
